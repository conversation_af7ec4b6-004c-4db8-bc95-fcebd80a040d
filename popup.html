<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
    
    .input-section {
      margin-bottom: 15px;
    }
    
    .input-section label {
      display: block;
      margin-bottom: 5px;
      font-size: 12px;
      opacity: 0.9;
    }
    
    .input-section input, .input-section textarea {
      width: 100%;
      padding: 8px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    .input-section textarea {
      height: 60px;
      resize: vertical;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      margin-top: 15px;
    }
    
    button {
      flex: 1;
      padding: 10px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .primary-btn {
      background: #4CAF50;
      color: white;
    }
    
    .primary-btn:hover {
      background: #45a049;
    }
    
    .secondary-btn {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }
    
    .secondary-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }
    
    .status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      font-size: 12px;
      text-align: center;
    }
    
    .status.success {
      background: rgba(76, 175, 80, 0.2);
    }
    
    .status.error {
      background: rgba(244, 67, 54, 0.2);
    }
    
    .status.loading {
      background: rgba(255, 193, 7, 0.2);
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🤖 AI网页助手</h1>
  </div>
  
  <div class="input-section">
    <label for="apiKey">API密钥 (OpenAI/其他):</label>
    <input type="password" id="apiKey" placeholder="输入你的API密钥">
  </div>
  
  <div class="input-section">
    <label for="prompt">自定义提示词:</label>
    <textarea id="prompt" placeholder="告诉AI你想要什么样的弹窗内容..."></textarea>
  </div>
  
  <div class="button-group">
    <button class="primary-btn" id="analyzeBtn">分析页面</button>
    <button class="secondary-btn" id="settingsBtn">设置</button>
  </div>
  
  <div id="status" class="status" style="display: none;"></div>
  
  <script src="popup.js"></script>
</body>
</html>
