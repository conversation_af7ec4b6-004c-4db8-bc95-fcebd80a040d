document.addEventListener('DOMContentLoaded', function() {
  const apiKeyInput = document.getElementById('apiKey');
  const promptInput = document.getElementById('prompt');
  const analyzeBtn = document.getElementById('analyzeBtn');
  const settingsBtn = document.getElementById('settingsBtn');
  const status = document.getElementById('status');

  // 加载保存的设置
  chrome.storage.sync.get(['apiKey', 'customPrompt'], function(result) {
    if (result.apiKey) {
      apiKeyInput.value = result.apiKey;
    }
    if (result.customPrompt) {
      promptInput.value = result.customPrompt;
    } else {
      promptInput.value = "请根据这个网页的内容，生成一个有趣、幽默或者有启发性的弹窗消息。可以是一个有趣的观察、一个相关的笑话、或者一个有用的提示。";
    }
  });

  // 保存设置
  function saveSettings() {
    chrome.storage.sync.set({
      apiKey: apiKeyInput.value,
      customPrompt: promptInput.value
    });
  }

  // 显示状态消息
  function showStatus(message, type) {
    status.textContent = message;
    status.className = `status ${type}`;
    status.style.display = 'block';
    
    if (type !== 'loading') {
      setTimeout(() => {
        status.style.display = 'none';
      }, 3000);
    }
  }

  // 分析页面按钮点击事件
  analyzeBtn.addEventListener('click', async function() {
    const apiKey = apiKeyInput.value.trim();
    const prompt = promptInput.value.trim();

    if (!apiKey) {
      showStatus('请输入API密钥', 'error');
      return;
    }

    if (!prompt) {
      showStatus('请输入提示词', 'error');
      return;
    }

    // 保存设置
    saveSettings();

    try {
      showStatus('正在分析页面...', 'loading');
      
      // 获取当前活动标签页
      const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
      
      // 向content script发送消息
      chrome.tabs.sendMessage(tab.id, {
        action: 'analyzePageWithAI',
        apiKey: apiKey,
        prompt: prompt
      }, function(response) {
        if (chrome.runtime.lastError) {
          showStatus('无法连接到页面', 'error');
          return;
        }
        
        if (response && response.success) {
          showStatus('分析完成！弹窗已显示', 'success');
        } else {
          showStatus(response?.error || '分析失败', 'error');
        }
      });
      
    } catch (error) {
      showStatus('发生错误: ' + error.message, 'error');
    }
  });

  // 设置按钮点击事件
  settingsBtn.addEventListener('click', function() {
    saveSettings();
    showStatus('设置已保存', 'success');
  });

  // 自动保存设置
  apiKeyInput.addEventListener('blur', saveSettings);
  promptInput.addEventListener('blur', saveSettings);
});
