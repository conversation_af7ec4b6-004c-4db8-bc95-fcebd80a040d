# AI网页助手 Chrome插件

一个智能的Chrome插件，能够通过自然语言与大模型交互，分析网页内容并生成有趣的弹窗。

## 功能特点

- 🤖 **AI驱动**: 使用OpenAI GPT模型分析网页内容
- 🎨 **美观界面**: 现代化的弹窗设计和用户界面
- 🔧 **可定制**: 支持自定义提示词和API配置
- 🌐 **全网支持**: 在任何网页上都能工作
- 💾 **设置保存**: 自动保存用户配置

## 安装方法

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹

## 使用方法

### 首次设置
1. 点击浏览器工具栏中的插件图标
2. 输入你的OpenAI API密钥
3. 可选：自定义提示词来控制AI生成的内容风格

### 分析网页
1. 在任何网页上点击插件图标
2. 点击"分析页面"按钮
3. AI将分析当前页面内容并显示有趣的弹窗

## API密钥获取

1. 访问 [OpenAI官网](https://platform.openai.com/)
2. 注册账号并登录
3. 进入API Keys页面
4. 创建新的API密钥
5. 将密钥复制到插件设置中

## 自定义提示词示例

- **幽默风格**: "请用幽默的方式总结这个网页，并给出一个有趣的评论"
- **学习助手**: "请提取这个页面的关键知识点，并给出学习建议"
- **新闻分析**: "请分析这篇文章的主要观点，并提供不同角度的思考"
- **购物助手**: "请分析这个商品页面，给出购买建议和注意事项"

## 文件结构

```
chrome-assistant/
├── manifest.json          # 插件配置文件
├── popup.html             # 弹窗界面
├── popup.js               # 弹窗逻辑
├── content.js             # 内容脚本
├── content.css            # 弹窗样式
├── background.js          # 后台脚本
├── icons/                 # 图标文件夹
└── README.md              # 说明文档
```

## 技术特点

- **Manifest V3**: 使用最新的Chrome扩展API
- **现代JavaScript**: ES6+语法和异步处理
- **响应式设计**: 适配不同屏幕尺寸
- **安全性**: API密钥本地存储，不上传到服务器

## 注意事项

- 需要有效的OpenAI API密钥才能使用
- API调用会产生费用，请注意使用量
- 某些网站可能有内容安全策略限制
- 建议在使用前测试API密钥是否有效

## 故障排除

### 常见问题

1. **弹窗不显示**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看浏览器控制台是否有错误

2. **API调用失败**
   - 验证API密钥格式
   - 检查OpenAI账户余额
   - 确认API使用限制

3. **插件无法加载**
   - 确认所有文件都在正确位置
   - 检查manifest.json语法
   - 重新加载插件

## 开发和贡献

欢迎提交Issue和Pull Request来改进这个项目！

### 开发环境设置
1. 克隆项目
2. 在Chrome中加载插件
3. 修改代码后重新加载插件进行测试

## 许可证

MIT License - 详见LICENSE文件

## 更新日志

### v1.0.0
- 初始版本发布
- 基本的AI分析和弹窗功能
- 支持自定义提示词
- 现代化UI设计
